<template>
  <nav class="nav-container">
    <div class="logo" @click="$emit('logo-click')" style="cursor: pointer">
      <div class="logo-icon"></div>
      <span style="margin-bottom: 7px">ANEW</span>
    </div>

    <div class="nav-tabs">
      <div
        v-for="tab in tabs"
        :key="tab"
        class="nav-tab"
        :class="{ active: activeTab === tab }"
        @click="$emit('tab-change', tab)"
      >
        {{ tab }}
      </div>
    </div>

    <div class="nav-buttons">
      <div class="search-container">
        <input
          type="text"
          class="search-input"
          :placeholder="searchPlaceholder"
          :value="searchQuery"
          @input="$emit('search-change', $event.target.value)"
        />
        <svg
          class="search-icon"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="11"
            cy="11"
            r="8"
            stroke="currentColor"
            stroke-width="2"
          />
          <path
            d="m21 21-4.35-4.35"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <button
        class="sort-button"
        @click="$emit('sort-habits')"
        title="Sort Habits"
      >
        <svg
          class="sort-icon"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8 7L12 3L16 7"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M12 3V15"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M16 17L12 21L8 17"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M12 21V9"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
      <button class="add-button" @click="$emit('add-habit')">+</button>
    </div>
  </nav>
</template>

<script>
import { computed } from "vue";

export default {
  name: "Navigation",
  props: {
    activeTab: {
      type: String,
      default: "DAILY",
    },
    searchQuery: {
      type: String,
      default: "",
    },
    mode: {
      type: String,
      default: "habits", // 'habits' or 'pomodoro'
    },
  },
  emits: [
    "tab-change",
    "add-habit",
    "sort-habits",
    "logo-click",
    "search-change",
  ],
  setup(props) {
    const tabs = ["DAILY", "CODING", "COURSES", "SCHOOL", "LIFE"];

    const searchPlaceholder = computed(() => {
      return props.mode === "pomodoro"
        ? "Search projects..."
        : "Search habits...";
    });

    return {
      tabs,
      searchPlaceholder,
    };
  },
};
</script>
