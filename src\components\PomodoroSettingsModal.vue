<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Settings</h2>
        <button class="close-button" @click="$emit('close')">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>

      <div class="settings-content">
        <!-- Settings Navigation -->
        <div class="settings-nav">
          <button
            class="nav-item"
            :class="{ active: activeTab === 'timer' }"
            @click="activeTab = 'timer'"
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"
              />
            </svg>
            Pomodoro Timer
          </button>
          <button
            class="nav-item"
            :class="{ active: activeTab === 'alarm' }"
            @click="activeTab = 'alarm'"
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"
              />
            </svg>
            Alarm Sound
          </button>
        </div>

        <!-- Timer Settings -->
        <div class="settings-panel" v-if="activeTab === 'timer'">
          <div class="setting-group">
            <label>Pomodoro Length (minutes)</label>
            <div class="input-with-select">
              <input
                type="number"
                v-model.number="settings.pomodoroLength"
                min="1"
                max="120"
                class="setting-input"
                @input="validateInput('pomodoroLength', $event)"
              />
              <select
                v-model="settings.pomodoroLength"
                class="setting-select-small"
              >
                <option value="15">15</option>
                <option value="20">20</option>
                <option value="25">25</option>
                <option value="30">30</option>
                <option value="45">45</option>
                <option value="60">60</option>
              </select>
            </div>
          </div>

          <div class="setting-group">
            <label>Short Break Length (minutes)</label>
            <div class="input-with-select">
              <input
                type="number"
                v-model.number="settings.shortBreakLength"
                min="1"
                max="60"
                class="setting-input"
                @input="validateInput('shortBreakLength', $event)"
              />
              <select
                v-model="settings.shortBreakLength"
                class="setting-select-small"
              >
                <option value="3">3</option>
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="15">15</option>
              </select>
            </div>
          </div>

          <div class="setting-group">
            <label>Long Break Length (minutes)</label>
            <div class="input-with-select">
              <input
                type="number"
                v-model.number="settings.longBreakLength"
                min="1"
                max="120"
                class="setting-input"
                @input="validateInput('longBreakLength', $event)"
              />
              <select
                v-model="settings.longBreakLength"
                class="setting-select-small"
              >
                <option value="10">10</option>
                <option value="15">15</option>
                <option value="20">20</option>
                <option value="30">30</option>
              </select>
            </div>
          </div>

          <div class="setting-group">
            <label>Long Break After</label>
            <select v-model="settings.longBreakAfter" class="setting-select">
              <option value="2">2 Pomodoros</option>
              <option value="3">3 Pomodoros</option>
              <option value="4">4 Pomodoros</option>
              <option value="5">5 Pomodoros</option>
            </select>
          </div>

          <div class="setting-group toggle-group">
            <label>Auto Start Next Pomodoro</label>
            <button
              class="toggle-button"
              :class="{ active: settings.autoStartPomodoro }"
              @click="settings.autoStartPomodoro = !settings.autoStartPomodoro"
            >
              <div class="toggle-slider"></div>
            </button>
          </div>

          <div class="setting-group toggle-group">
            <label>Auto Start Break</label>
            <button
              class="toggle-button"
              :class="{ active: settings.autoStartBreak }"
              @click="settings.autoStartBreak = !settings.autoStartBreak"
            >
              <div class="toggle-slider"></div>
            </button>
          </div>
        </div>

        <!-- Alarm Settings -->
        <div class="settings-panel" v-if="activeTab === 'alarm'">
          <div class="setting-group">
            <label>Pomodoro End Alarm</label>
            <div class="alarm-options">
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    type="radio"
                    value="beep"
                    v-model="settings.pomodoroAlarm"
                    name="pomodoroAlarm"
                  />
                  <span class="radio-custom"></span>
                  <span class="radio-text">Alarm Beep</span>
                </label>
                <label class="radio-option">
                  <input
                    type="radio"
                    value="guitar"
                    v-model="settings.pomodoroAlarm"
                    name="pomodoroAlarm"
                  />
                  <span class="radio-custom"></span>
                  <span class="radio-text">Guitar</span>
                </label>
              </div>
              <button class="test-button" @click="testAlarm('pomodoro')">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8,5.14V19.14L19,12.14L8,5.14Z" />
                </svg>
                Test
              </button>
            </div>
          </div>

          <div class="setting-group">
            <label>Break End Alarm</label>
            <div class="alarm-options">
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    type="radio"
                    value="beep"
                    v-model="settings.breakAlarm"
                    name="breakAlarm"
                  />
                  <span class="radio-custom"></span>
                  <span class="radio-text">Alarm Beep</span>
                </label>
                <label class="radio-option">
                  <input
                    type="radio"
                    value="guitar"
                    v-model="settings.breakAlarm"
                    name="breakAlarm"
                  />
                  <span class="radio-custom"></span>
                  <span class="radio-text">Guitar</span>
                </label>
              </div>
              <button class="test-button" @click="testAlarm('break')">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8,5.14V19.14L19,12.14L8,5.14Z" />
                </svg>
                Test
              </button>
            </div>
          </div>

          <div class="setting-group">
            <label>Alarm Volume</label>
            <div class="volume-control">
              <svg viewBox="0 0 24 24" fill="currentColor" class="volume-icon">
                <path
                  d="M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.85 14,18.71V20.77C18.01,19.86 21,16.28 21,12C21,7.72 18.01,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z"
                />
              </svg>
              <input
                type="range"
                v-model.number="settings.alarmVolume"
                min="0"
                max="100"
                step="5"
                class="volume-slider"
                @input="updateVolume"
              />
              <span class="volume-value">{{ settings.alarmVolume }}%</span>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="save-button" @click="saveSettings">Save Settings</button>
        <button class="cancel-button" @click="$emit('close')">Cancel</button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from "vue";

export default {
  name: "PomodoroSettingsModal",
  emits: ["close", "save"],
  setup(_, { emit }) {
    const activeTab = ref("timer");

    const settings = ref({
      pomodoroLength: 25,
      shortBreakLength: 5,
      longBreakLength: 15,
      longBreakAfter: 4,
      autoStartPomodoro: false,
      autoStartBreak: false,
      pomodoroAlarm: "beep",
      breakAlarm: "beep",
      alarmVolume: 50,
    });

    // Audio instances for alarm sounds
    const alarmSounds = {
      beep: null,
      guitar: null,
    };

    // Initialize audio files
    const initializeAudio = () => {
      try {
        // Create audio instances
        alarmSounds.beep = new Audio();
        alarmSounds.guitar = new Audio();

        // Load the audio files
        alarmSounds.beep.src = "/Alarm Beep.wav";
        alarmSounds.guitar.src = "/Guitar.wav";

        // Set initial volume
        Object.values(alarmSounds).forEach((audio) => {
          if (audio) {
            audio.volume = settings.value.alarmVolume / 100;
          }
        });
      } catch (error) {
        console.error("Error initializing audio:", error);
      }
    };

    // Input validation
    const validateInput = (field, event) => {
      const value = parseInt(event.target.value);
      const limits = {
        pomodoroLength: { min: 1, max: 120 },
        shortBreakLength: { min: 1, max: 60 },
        longBreakLength: { min: 1, max: 120 },
      };

      if (limits[field]) {
        const { min, max } = limits[field];
        if (value < min) {
          settings.value[field] = min;
        } else if (value > max) {
          settings.value[field] = max;
        }
      }
    };

    // Test alarm sound
    // Test alarm sound
    const testAlarm = (type) => {
      try {
        const alarmType =
          type === "pomodoro"
            ? settings.value.pomodoroAlarm
            : settings.value.breakAlarm;
        const audio = alarmSounds[alarmType];

        if (audio) {
          audio.currentTime = 0;
          audio.volume = settings.value.alarmVolume / 100;
          audio.play().catch((error) => {
            console.error("Error playing alarm:", error);
          });
        }
      } catch (error) {
        console.error("Error testing alarm:", error);
      }
    };
    // Update volume for all audio instances
    const updateVolume = () => {
      const volume = settings.value.alarmVolume / 100;
      Object.values(alarmSounds).forEach((audio) => {
        if (audio) {
          audio.volume = volume;
        }
      });
    };

    // Play alarm automatically (called from other components)
    const playAlarm = (eventType) => {
      try {
        const currentSettings = JSON.parse(
          localStorage.getItem("pomodoroSettings") || "{}"
        );
        const alarmType =
          eventType === "pomodoroEnd"
            ? currentSettings.pomodoroAlarm || "beep"
            : currentSettings.breakAlarm || "beep";
        const volume = (currentSettings.alarmVolume || 50) / 100;

        // Initialize audio if not already done
        if (!alarmSounds.beep && !alarmSounds.guitar) {
          initializeAudio();
        }

        const audio = alarmSounds[alarmType];
        if (audio) {
          audio.currentTime = 0;
          audio.volume = volume;
          audio.play().catch((error) => {
            console.error("Error playing automatic alarm:", error);
          });
        }
      } catch (error) {
        console.error("Error in automatic alarm:", error);
      }
    };

    // Load settings from database
    const loadSettings = async () => {
      try {
        const response = await fetch("/api/pomodoro/settings");
        const data = await response.json();
        if (data.success) {
          settings.value = { ...settings.value, ...data.settings };
        }
      } catch (error) {
        console.error("Error loading settings from database:", error);
        // Fallback to localStorage
        try {
          const saved = localStorage.getItem("pomodoroSettings");
          if (saved) {
            const savedSettings = JSON.parse(saved);
            settings.value = { ...settings.value, ...savedSettings };
          }
        } catch (fallbackError) {
          console.error(
            "Error loading settings from localStorage:",
            fallbackError
          );
        }
      }
    };

    // Save settings to database and emit to parent
    const saveSettings = async () => {
      try {
        // Save to database
        const response = await fetch("/api/pomodoro/settings", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(settings.value),
        });
        const data = await response.json();

        if (data.success) {
          // Also save to localStorage for backward compatibility
          localStorage.setItem(
            "pomodoroSettings",
            JSON.stringify(settings.value)
          );
          emit("save", settings.value);
          emit("close");
        } else {
          console.error("Error saving settings to database:", data.error);
          // Fallback to localStorage only
          localStorage.setItem(
            "pomodoroSettings",
            JSON.stringify(settings.value)
          );
          emit("save", settings.value);
          emit("close");
        }
      } catch (error) {
        console.error("Error saving settings:", error);
        // Fallback to localStorage only
        try {
          localStorage.setItem(
            "pomodoroSettings",
            JSON.stringify(settings.value)
          );
          emit("save", settings.value);
          emit("close");
        } catch (fallbackError) {
          console.error(
            "Error saving settings to localStorage:",
            fallbackError
          );
        }
      }
    };

    onMounted(() => {
      loadSettings();
      initializeAudio();
    });

    return {
      activeTab,
      settings,
      saveSettings,
      validateInput,
      testAlarm,
      updateVolume,
    };
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.modal-content {
  background: #2a2a2a;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  color: white;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #444;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #ccc;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.close-button svg {
  width: 20px;
  height: 20px;
}

.settings-content {
  display: flex;
  flex: 1;
  min-height: 400px;
  overflow: hidden;
}

.settings-nav {
  width: 200px;
  background: #333;
  padding: 16px 0;
}

.nav-item {
  width: 100%;
  background: none;
  border: none;
  color: #ccc;
  padding: 12px 20px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9rem;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item.active {
  background: #ff4444;
  color: white;
}

.nav-item svg {
  width: 16px;
  height: 16px;
}

.settings-panel {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  padding-bottom: 80px; /* Add space for footer */
}

.setting-group {
  margin-bottom: 24px;
}

.setting-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #ddd;
}

.setting-select {
  width: 100%;
  background: #444;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 12px;
  color: white;
  font-size: 1rem;
  cursor: pointer;
}

.setting-select:focus {
  outline: none;
  border-color: #ff4444;
}

/* Input with Select Styles */
.input-with-select {
  display: flex;
  gap: 8px;
  align-items: center;
}

.setting-input {
  flex: 1;
  background: #444;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 12px;
  color: white;
  font-size: 1rem;
  min-width: 80px;
}

.setting-input:focus {
  outline: none;
  border-color: #ff4444;
}

.setting-select-small {
  background: #444;
  border: 1px solid #555;
  border-radius: 8px;
  padding: 12px;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  min-width: 80px;
}

.setting-select-small:focus {
  outline: none;
  border-color: #ff4444;
}

.toggle-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-button {
  width: 50px;
  height: 26px;
  background: #555;
  border: none;
  border-radius: 13px;
  position: relative;
  cursor: pointer;
  transition: background 0.3s;
}

.toggle-button.active {
  background: #ff4444;
}

.toggle-slider {
  width: 22px;
  height: 22px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.3s;
}

.toggle-button.active .toggle-slider {
  transform: translateX(24px);
}

.radio-group {
  display: flex;
  gap: 24px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 0.9rem;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #666;
  border-radius: 50%;
  position: relative;
  transition: border-color 0.2s;
}

.radio-option input[type="radio"]:checked + .radio-custom {
  border-color: #ff4444;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
  content: "";
  width: 8px;
  height: 8px;
  background: #ff4444;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Alarm Options Styles */
.alarm-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.test-button {
  background: #555;
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 60px;
}

.test-button:hover {
  background: #666;
}

.test-button svg {
  width: 12px;
  height: 12px;
}

/* Volume Control Styles */
.volume-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.volume-icon {
  width: 20px;
  height: 20px;
  color: #ccc;
}

.volume-slider {
  flex: 1;
  height: 6px;
  background: #555;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #ff4444;
  border-radius: 50%;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #ff4444;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.volume-value {
  font-size: 0.9rem;
  color: #ccc;
  min-width: 40px;
  text-align: right;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #444;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.save-button {
  background: #ff4444;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s;
}

.save-button:hover {
  background: #e63939;
}

.cancel-button {
  background: #555;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s;
}

.cancel-button:hover {
  background: #666;
}
</style>
