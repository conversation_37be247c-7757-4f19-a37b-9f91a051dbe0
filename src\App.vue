<template>
  <div id="app">
    <!-- Habit Tracking Mode -->
    <div v-if="currentMode === 'habits'">
      <Navigation
        :activeTab="activeTab"
        :searchQuery="searchQuery"
        mode="habits"
        @tab-change="activeTab = $event"
        @add-habit="showAddModal = true"
        @sort-habits="showSortModal = true"
        @logo-click="switchToPomodoro"
        @search-change="searchQuery = $event"
      />

      <main class="main-content">
        <div class="habits-grid">
          <HabitCard
            v-for="habit in filteredHabits"
            :key="habit.id"
            :habit="habit"
            @toggle-day="toggleHabitDay"
            @open-history="openHabitHistory"
          />
        </div>
      </main>
    </div>

    <!-- Pomodoro Mode -->
    <div v-else-if="currentMode === 'pomodoro'">
      <PomodoroView @back-to-habits="switchToHabits" />
    </div>

    <AddHabitModal
      v-if="showAddModal"
      @close="showAddModal = false"
      @add-habit="addHabit"
      :error-message="addHabitError"
      :default-category="activeTab"
    />

    <HabitHistoryModal
      v-if="showHistoryModal"
      :habit="selectedHabit"
      @close="showHistoryModal = false"
      @toggle-day="toggleHabitDay"
      @edit-habit="editHabit"
      @delete-habit="deleteHabit"
    />

    <HabitSortModal
      v-if="showSortModal"
      :habits="habits"
      :activeTab="activeTab"
      @close="showSortModal = false"
      @save-order="saveHabitOrder"
    />
  </div>
</template>

<script>
import { ref, onMounted, computed } from "vue";
import Navigation from "./components/Navigation.vue";
import HabitCard from "./components/HabitCard.vue";
import AddHabitModal from "./components/AddHabitModal.vue";
import HabitHistoryModal from "./components/HabitHistoryModal.vue";
import HabitSortModal from "./components/HabitSortModal.vue";
import PomodoroView from "./components/PomodoroView.vue";

export default {
  name: "App",
  components: {
    Navigation,
    HabitCard,
    AddHabitModal,
    HabitHistoryModal,
    HabitSortModal,
    PomodoroView,
  },
  setup() {
    const currentMode = ref("habits"); // 'habits' or 'pomodoro'
    const activeTab = ref("DAILY");
    const searchQuery = ref("");
    const showAddModal = ref(false);
    const showSortModal = ref(false);
    const habits = ref([]);
    const addHabitError = ref("");
    const showHistoryModal = ref(false);
    const selectedHabit = ref(null);

    // Mode switching functions
    const switchToPomodoro = () => {
      currentMode.value = "pomodoro";
    };

    const switchToHabits = () => {
      currentMode.value = "habits";
    };

    const loadHabits = async () => {
      try {
        const response = await fetch("/api/habits");
        const data = await response.json();
        if (data.success) {
          console.log(
            "🔍 Loaded habits from server:",
            data.habits.map((h) => ({
              id: h.id,
              name: h.name,
              order_position: h.order_position,
            }))
          );

          // Check if we need to initialize order positions per category
          console.log(
            "🔥 FRONTEND: Starting order position initialization check"
          );
          const habitsByCategory = {};
          data.habits.forEach((habit) => {
            if (!habitsByCategory[habit.category]) {
              habitsByCategory[habit.category] = [];
            }
            habitsByCategory[habit.category].push(habit);
          });

          let needsInitialization = false;
          const habitOrdersToUpdate = [];

          // Check each category separately and assign proper order positions
          Object.keys(habitsByCategory).forEach((category) => {
            const categoryHabits = habitsByCategory[category];

            // Check if this category needs initialization
            // We need to check if there are gaps in the order positions or multiple habits with the same position
            const orderPositions = categoryHabits.map(
              (h) => h.order_position || 0
            );
            const uniquePositions = [...new Set(orderPositions)];
            const hasGaps = uniquePositions.length !== categoryHabits.length;
            const hasDuplicates =
              orderPositions.length !== uniquePositions.length;
            const hasNullUndefined = categoryHabits.some(
              (h) => h.order_position === null || h.order_position === undefined
            );

            const categoryNeedsInit =
              hasNullUndefined || hasGaps || hasDuplicates;

            console.log(
              `🔍 Category ${category} needs initialization:`,
              categoryNeedsInit
            );
            console.log(
              `🔍 Category ${category} habits:`,
              categoryHabits.map((h) => ({
                id: h.id,
                name: h.name,
                order_position: h.order_position,
              }))
            );

            if (categoryNeedsInit) {
              needsInitialization = true;
              // Sort by current order_position first, then by ID for consistency
              categoryHabits.sort((a, b) => {
                if (a.order_position !== b.order_position) {
                  return (a.order_position || 0) - (b.order_position || 0);
                }
                return a.id - b.id;
              });

              // Assign sequential order positions within this category
              categoryHabits.forEach((habit, index) => {
                habitOrdersToUpdate.push({
                  id: habit.id,
                  order_position: index,
                });
                habit.order_position = index; // Update immediately for UI
              });
            }
          });

          if (needsInitialization && habitOrdersToUpdate.length > 0) {
            console.log(
              "🔧 Initializing order positions for habits:",
              habitOrdersToUpdate
            );

            try {
              const response = await fetch("/api/habits/reorder", {
                method: "PUT",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  habitOrders: habitOrdersToUpdate,
                }),
              });

              const result = await response.json();
              if (result.success) {
                console.log("✅ Order positions initialized successfully");
              } else {
                console.error(
                  "❌ Failed to initialize order positions:",
                  result.error
                );
              }
            } catch (error) {
              console.error("❌ Error initializing order positions:", error);
            }
          }

          // Ensure all habits have order_position values
          const habitsWithOrder = data.habits.map((habit, index) => ({
            ...habit,
            order_position:
              habit.order_position !== undefined &&
              habit.order_position !== null
                ? habit.order_position
                : index,
          }));

          habits.value = habitsWithOrder;
          console.log(
            "Habits with order assigned:",
            habitsWithOrder.map((h) => ({
              id: h.id,
              name: h.name,
              order_position: h.order_position,
            }))
          );
        }
      } catch (error) {
        console.error("Error loading habits:", error);
      }
    };

    const addHabit = async (habitData) => {
      addHabitError.value = ""; // Clear previous errors

      try {
        const response = await fetch("/api/habits", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(habitData),
        });
        const data = await response.json();
        if (data.success) {
          habits.value.push(data.habit);
          showAddModal.value = false;
          addHabitError.value = "";
        } else {
          // Show error message in modal
          addHabitError.value = data.error || "Failed to add habit";
        }
      } catch (error) {
        console.error("Error adding habit:", error);
        addHabitError.value = "Failed to add habit. Please try again.";
      }
    };

    const toggleHabitDay = async (habitId, date) => {
      try {
        const response = await fetch("/api/progress", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            habit_id: habitId,
            date: date,
          }),
        });
        const data = await response.json();
        if (data.success) {
          // Update the habit's progress in the local state
          const habit = habits.value.find((h) => h.id === habitId);
          if (habit) {
            if (!habit.progress) habit.progress = {};
            habit.progress[date] = data.completed;
          }
        }
      } catch (error) {
        console.error("Error toggling habit day:", error);
      }
    };

    const openHabitHistory = (habit) => {
      selectedHabit.value = habit;
      showHistoryModal.value = true;
    };

    const editHabit = async (habitData) => {
      console.log("App.vue: editHabit called with:", habitData);
      try {
        const response = await fetch(`/api/habits/${habitData.id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: habitData.name,
            color: habitData.color,
            category: habitData.category,
          }),
        });
        console.log("Edit response status:", response.status);
        const data = await response.json();
        console.log("Edit response data:", data);
        if (data.success) {
          // Update the habit in the local state
          const habitIndex = habits.value.findIndex(
            (h) => h.id === habitData.id
          );
          if (habitIndex !== -1) {
            habits.value[habitIndex] = {
              ...habits.value[habitIndex],
              ...data.habit,
            };
            // Update selectedHabit if it's the same habit
            if (
              selectedHabit.value &&
              selectedHabit.value.id === habitData.id
            ) {
              selectedHabit.value = { ...selectedHabit.value, ...data.habit };
            }
          }
          console.log("Habit updated successfully");
        } else {
          console.error("Edit failed:", data.error);
        }
      } catch (error) {
        console.error("Error editing habit:", error);
      }
    };

    const deleteHabit = async (habitId) => {
      console.log("App.vue: deleteHabit called with:", habitId);
      try {
        const response = await fetch(`/api/habits/${habitId}`, {
          method: "DELETE",
        });
        console.log("Delete response status:", response.status);
        const data = await response.json();
        console.log("Delete response data:", data);
        if (data.success) {
          // Remove the habit from the local state
          habits.value = habits.value.filter((h) => h.id !== habitId);
          showHistoryModal.value = false;
          selectedHabit.value = null;
          console.log("Habit deleted successfully");
        } else {
          console.error("Delete failed:", data.error);
        }
      } catch (error) {
        console.error("Error deleting habit:", error);
      }
    };

    // Save habit order from sort modal
    const saveHabitOrder = async (sortedHabits) => {
      console.log("💾 Saving habit order from sort modal:", sortedHabits);

      try {
        // Update the habits array with new order positions
        sortedHabits.forEach((sortedHabit, index) => {
          const habitIndex = habits.value.findIndex(
            (h) => h.id === sortedHabit.id
          );
          if (habitIndex !== -1) {
            habits.value[habitIndex].order_position = index;
          }
        });

        // Create the order array for the server
        const habitOrders = sortedHabits.map((habit, index) => ({
          id: habit.id,
          order_position: index,
        }));

        console.log("🔄 Sending new order to server:", habitOrders);

        const response = await fetch("/api/habits/reorder", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            habitOrders: habitOrders,
          }),
        });

        const data = await response.json();
        if (data.success) {
          console.log("✅ Habit order saved successfully");
          showSortModal.value = false;

          // Force reactivity update
          habits.value = [...habits.value];
        } else {
          console.error("❌ Failed to save habit order:", data.error);
        }
      } catch (error) {
        console.error("❌ Error saving habit order:", error);
      }
    };

    // Computed property to filter habits by active tab and search query
    const filteredHabits = computed(() => {
      let filtered = habits.value.filter(
        (habit) => habit.category === activeTab.value
      );

      // Apply search filter if search query exists
      if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase().trim();
        filtered = filtered.filter((habit) =>
          habit.name.toLowerCase().includes(query)
        );
      }

      const sorted = filtered.sort(
        (a, b) => (a.order_position || 0) - (b.order_position || 0)
      );

      console.log(
        "Filtered habits for",
        activeTab.value,
        searchQuery.value ? `with search "${searchQuery.value}"` : "",
        ":",
        sorted.map((h) => ({
          id: h.id,
          name: h.name,
          order_position: h.order_position,
        }))
      );

      return sorted;
    });

    // All habits now use a fixed start date (January 1, 2025) for perfect uniformity
    // No need for global earliest date calculation

    onMounted(() => {
      loadHabits();
    });

    return {
      currentMode,
      activeTab,
      searchQuery,
      showAddModal,
      showSortModal,
      habits,
      filteredHabits,
      addHabitError,
      showHistoryModal,
      selectedHabit,
      addHabit,
      toggleHabitDay,
      openHabitHistory,
      editHabit,
      deleteHabit,
      saveHabitOrder,
      switchToPomodoro,
      switchToHabits,
    };
  },
};
</script>
